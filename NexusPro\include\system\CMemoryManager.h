#pragma once

/*
 * CMemoryManager.h - RESOURCE ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * Memory allocation tracking and management
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive memory management
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CResourceManager;
class CSystemManager;

// Memory allocation types
enum MemoryAllocationType {
    MEMORY_TYPE_GENERAL = 0,
    MEMORY_TYPE_TEXTURE = 1,
    MEMORY_TYPE_SOUND = 2,
    MEMORY_TYPE_MODEL = 3,
    MEMORY_TYPE_EFFECT = 4,
    MEMORY_TYPE_NETWORK = 5,
    MEMORY_TYPE_DATABASE = 6,
    MEMORY_TYPE_CACHE = 7,
    MEMORY_TYPE_TEMPORARY = 8,
    MEMORY_TYPE_SYSTEM = 9
};

// Memory pool types
enum MemoryPoolType {
    POOL_TYPE_SMALL = 0,    // < 1KB
    POOL_TYPE_MEDIUM = 1,   // 1KB - 64KB
    POOL_TYPE_LARGE = 2,    // 64KB - 1MB
    POOL_TYPE_HUGE = 3      // > 1MB
};

// Memory allocation result
enum MemoryResult {
    MEMORY_SUCCESS = 0,
    MEMORY_ERROR_OUT_OF_MEMORY = 1,
    MEMORY_ERROR_INVALID_SIZE = 2,
    MEMORY_ERROR_INVALID_POINTER = 3,
    MEMORY_ERROR_DOUBLE_FREE = 4,
    MEMORY_ERROR_CORRUPTION = 5,
    MEMORY_ERROR_POOL_FULL = 6,
    MEMORY_ERROR_UNKNOWN = 7
};

// Memory allocation information
struct MemoryAllocation {
    void* pMemory;
    size_t nSize;
    MemoryAllocationType type;
    MemoryPoolType poolType;
    DWORD dwAllocTime;
    DWORD dwLastAccess;
    DWORD dwAccessCount;
    const char* szFile;
    int nLine;
    const char* szFunction;
    bool bActive;
    bool bLocked;
    DWORD dwChecksum;
};

// Memory pool information
struct MemoryPool {
    MemoryPoolType type;
    size_t nBlockSize;
    size_t nBlockCount;
    size_t nUsedBlocks;
    size_t nFreeBlocks;
    void* pPoolMemory;
    std::vector<void*> freeBlocks;
    std::vector<void*> usedBlocks;
    DWORD dwCreationTime;
    DWORD dwLastAllocation;
    bool bActive;
    mutable std::mutex poolMutex;
};

// Memory manager configuration
struct MemoryManagerConfig {
    bool bEnabled;
    bool bPoolingEnabled;
    bool bTrackingEnabled;
    bool bDebuggingEnabled;
    bool bLeakDetectionEnabled;
    bool bCorruptionDetectionEnabled;
    bool bStatisticsEnabled;
    size_t nMaxTotalMemory;
    size_t nMaxPoolMemory;
    size_t nSmallBlockSize;
    size_t nMediumBlockSize;
    size_t nLargeBlockSize;
    size_t nSmallPoolBlocks;
    size_t nMediumPoolBlocks;
    size_t nLargePoolBlocks;
    DWORD dwCleanupInterval;
    DWORD dwLeakCheckInterval;
};

// Memory manager statistics
struct MemoryManagerStatistics {
    size_t nTotalAllocations;
    size_t nTotalDeallocations;
    size_t nCurrentAllocations;
    size_t nPeakAllocations;
    size_t nTotalMemoryAllocated;
    size_t nTotalMemoryDeallocated;
    size_t nCurrentMemoryUsed;
    size_t nPeakMemoryUsed;
    size_t nPoolMemoryUsed;
    size_t nSystemMemoryUsed;
    DWORD dwAllocationFailures;
    DWORD dwDeallocationFailures;
    DWORD dwMemoryLeaks;
    DWORD dwCorruptionDetections;
    DWORD dwAverageAllocationSize;
    DWORD dwLargestAllocation;
    DWORD dwSmallestAllocation;
    DWORD dwLastAllocationTime;
    DWORD dwLastDeallocationTime;
};

/*
 * CMemoryManager - Memory allocation tracking and management
 * Manages all memory operations, pooling, tracking, and debugging
 * Enhanced with comprehensive memory management and RF Online compatibility
 */
class CMemoryManager {
public:
    // Singleton access
    static CMemoryManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // Memory allocation methods
    void* Allocate(size_t nSize);
    void* Allocate(size_t nSize, MemoryAllocationType type);
    void* Allocate(size_t nSize, MemoryAllocationType type, const char* szFile, int nLine, const char* szFunction);
    void* AllocateAligned(size_t nSize, size_t nAlignment);
    void* AllocateZeroed(size_t nSize);
    void* Reallocate(void* pMemory, size_t nNewSize);

    // Memory deallocation methods
    void Deallocate(void* pMemory);
    void Deallocate(void* pMemory, const char* szFile, int nLine, const char* szFunction);

    // Memory pool methods
    bool CreatePool(MemoryPoolType type, size_t nBlockSize, size_t nBlockCount);
    void DestroyPool(MemoryPoolType type);
    void* AllocateFromPool(MemoryPoolType type);
    void DeallocateToPool(void* pMemory, MemoryPoolType type);
    bool IsPoolMemory(void* pMemory);

    // Memory tracking methods
    bool GetAllocationInfo(void* pMemory, MemoryAllocation& info);
    bool IsValidPointer(void* pMemory);
    bool IsAllocatedMemory(void* pMemory);
    size_t GetAllocationSize(void* pMemory);
    MemoryAllocationType GetAllocationType(void* pMemory);

    // Memory debugging methods
    bool CheckMemoryIntegrity();
    bool CheckMemoryIntegrity(void* pMemory);
    bool DetectMemoryLeaks();
    bool DetectMemoryCorruption();
    void DumpMemoryAllocations();
    void DumpMemoryStatistics();

    // Memory utility methods
    void* MemoryCopy(void* pDest, const void* pSrc, size_t nSize);
    void* MemoryMove(void* pDest, const void* pSrc, size_t nSize);
    void* MemorySet(void* pDest, int nValue, size_t nSize);
    int MemoryCompare(const void* pMem1, const void* pMem2, size_t nSize);
    void MemoryZero(void* pMemory, size_t nSize);

    // System memory information
    size_t GetTotalSystemMemory();
    size_t GetAvailableSystemMemory();
    size_t GetUsedSystemMemory();
    float GetMemoryUsagePercentage();

    // Configuration and statistics
    const MemoryManagerConfig& GetConfig() const { return m_Config; }
    const MemoryManagerStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Utility methods
    bool IsMemoryManagerEnabled() const { return m_bInitialized && m_Config.bEnabled; }
    void LogMemoryOperation(const char* szOperation, void* pMemory, size_t nSize, MemoryResult result);

private:
    // Private constructor for singleton
    CMemoryManager();
    ~CMemoryManager();

    // Prevent copying
    CMemoryManager(const CMemoryManager&) = delete;
    CMemoryManager& operator=(const CMemoryManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializePools();
    void ShutdownPools();
    MemoryResult ProcessAllocation(void* pMemory, size_t nSize, MemoryAllocationType type, const char* szFile, int nLine, const char* szFunction);
    MemoryResult ProcessDeallocation(void* pMemory, const char* szFile, int nLine, const char* szFunction);
    void UpdateStatistics();
    void UpdatePools();
    void CleanupExpiredAllocations();

    // Pool management
    MemoryPool* GetPool(MemoryPoolType type);
    MemoryPoolType DeterminePoolType(size_t nSize);
    void* AllocateFromSystemMemory(size_t nSize);
    void DeallocateToSystemMemory(void* pMemory);

    // Tracking and debugging
    void AddAllocation(void* pMemory, size_t nSize, MemoryAllocationType type, const char* szFile, int nLine, const char* szFunction);
    void RemoveAllocation(void* pMemory);
    MemoryAllocation* FindAllocation(void* pMemory);
    DWORD CalculateMemoryChecksum(void* pMemory, size_t nSize);
    bool ValidateMemoryChecksum(const MemoryAllocation& allocation);

    // Member variables
    static CMemoryManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bPoolsInitialized;
    bool m_bTrackingActive;
    MemoryManagerConfig m_Config;
    MemoryManagerStatistics m_Statistics;

    // Memory pools
    std::map<MemoryPoolType, MemoryPool> m_MemoryPools;

    // Memory tracking
    std::map<void*, MemoryAllocation> m_Allocations;
    std::vector<void*> m_LeakedMemory;

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_AllocationMutex;
    mutable std::mutex m_PoolMutex;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwLastLeakCheck;
    DWORD m_dwAllocationStartTime;
};
