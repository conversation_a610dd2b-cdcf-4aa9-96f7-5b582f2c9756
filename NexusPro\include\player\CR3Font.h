/**
 * @file CR3Font.h
 * @brief Direct3D font rendering and text measurement system
 * @details Comprehensive font system for Direct3D text rendering with GDI integration
 *
 * Original Memory Addresses:
 * - CR3Font::InvalidateDeviceObjects: 0x140528820
 * - CR3Font::CalcStrIndexPitInWidthA: 0x140527DD0
 * - CR3Font::CalcStrIndexPitInWidthW: 0x140527D80
 * - CR3Font::InitDeviceObjects: 0x140526D30
 * - CR3Font::PrivateInit: 0x140527E80
 * - CR3Font::MemAllocate: Memory allocation for font cache
 * - CR3Font::ClearCache: Font cache clearing
 * - CR3Font::PrivateRelease: Resource cleanup
 *
 * Memory Layout Analysis:
 * - QWORD at offset +0: IDirect3DDevice8 pointer
 * - DWORD at offset +80: Scale factor (1065353216 = 1.0f)
 * - DWORD at offset +84: Font size parameter
 * - QWORD at offset +112: HBITMAP handle
 * - QWORD at offset +120: HDC handle
 * - QWORD at offset +128: HFONT handle
 * - QWORD at offset +136: DIB section data pointer
 * - DWORD at offset +88-92: Character dimensions
 * - DWORD at offset +96: Bitmap flags
 * - DWORD at offset +100-104: Device flags
 * - DWORD at offset +144-156: Texture dimensions and cache info
 * - DWORD at offset +160: Bitmap data
 * - DWORD at offset +2092: Font style flags (offset +523*4)
 * - DWORD at offset +108: Character set
 *
 * Decompiled from: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @version 1.0.0
 */

#pragma once

#include "../common/RFProtocol.h"
#include "../common/Types.h"
#include "../common/SecurityManager.h"
#include "../common/WindowsTypes.h"
#include <memory>
#include <string>
#include <atomic>
#include <mutex>

// Forward declarations
struct IDirect3DDevice8;
struct tagSIZE;

/**
 * @enum FontStyle
 * @brief Font style flags for text rendering
 */
enum class FontStyle : std::uint32_t {
    NORMAL = 0x00,          ///< Normal font style
    BOLD = 0x01,            ///< Bold font weight
    ITALIC = 0x02,          ///< Italic font style
    UNDERLINE = 0x04,       ///< Underlined text
    STRIKEOUT = 0x08        ///< Strikeout text
};

/**
 * @struct FontMetrics
 * @brief Font measurement and metrics information
 */
struct FontMetrics {
    std::uint32_t dwCharWidth{0};       ///< Character width in pixels
    std::uint32_t dwCharHeight{0};      ///< Character height in pixels
    std::uint32_t dwTextureWidth{0};    ///< Texture width
    std::uint32_t dwTextureHeight{0};   ///< Texture height
    std::uint32_t dwRowCount{0};        ///< Number of character rows
    std::uint32_t dwColCount{0};        ///< Number of character columns

    FontMetrics() noexcept = default;

    bool IsValid() const noexcept {
        return dwCharWidth > 0 && dwCharHeight > 0;
    }

    void Clear() noexcept {
        dwCharWidth = 0;
        dwCharHeight = 0;
        dwTextureWidth = 0;
        dwTextureHeight = 0;
        dwRowCount = 0;
        dwColCount = 0;
    }
};

/**
 * @struct DeviceObjects
 * @brief Direct3D and GDI device objects for font rendering
 */
struct DeviceObjects {
    IDirect3DDevice8* pDevice{nullptr}; ///< Direct3D device pointer
    HDC hDC{nullptr};                   ///< Device context handle
    HBITMAP hBitmap{nullptr};           ///< Bitmap handle
    HFONT hFont{nullptr};               ///< Font handle
    void* pBitmapData{nullptr};         ///< DIB section data pointer
    bool bInitialized{false};           ///< Initialization status

    DeviceObjects() noexcept = default;

    bool IsValid() const noexcept {
        return pDevice != nullptr && hDC != nullptr && hBitmap != nullptr && hFont != nullptr;
    }

    void Clear() noexcept {
        pDevice = nullptr;
        hDC = nullptr;
        hBitmap = nullptr;
        hFont = nullptr;
        pBitmapData = nullptr;
        bInitialized = false;
    }
};

/**
 * @class CR3Font
 * @brief Manages Direct3D font rendering with GDI integration and comprehensive text measurement
 *
 * This class handles all aspects of font rendering including:
 * - Direct3D device object management
 * - GDI font creation and text measurement
 * - Character width calculation for both ANSI and Unicode
 * - Font cache management and optimization
 * - Device context and bitmap management
 * - Memory-safe resource management with RAII compliance
 * - Integration with Direct3D rendering pipeline
 *
 * Memory Layout (based on decompiled analysis):
 * - Offset +0: IDirect3DDevice8 pointer
 * - Offset +80: Scale factor (float, default 1.0f)
 * - Offset +84: Font size parameter
 * - Offset +88-92: Character dimensions
 * - Offset +96: Bitmap flags
 * - Offset +100-104: Device flags
 * - Offset +108: Character set
 * - Offset +112: HBITMAP handle
 * - Offset +120: HDC handle
 * - Offset +128: HFONT handle
 * - Offset +136: DIB section data pointer
 * - Offset +144-156: Texture dimensions and cache info
 * - Offset +160: Bitmap data
 * - Offset +2092: Font style flags
 */
class CR3Font {
public:
    /**
     * @brief Default constructor with secure initialization
     *
     * Initializes font system with debug memory patterns and secure defaults.
     */
    CR3Font();

    /**
     * @brief Virtual destructor with RAII cleanup
     *
     * Ensures proper cleanup of Direct3D and GDI resources.
     */
    virtual ~CR3Font();

    // Disable copy operations for security
    CR3Font(const CR3Font&) = delete;
    CR3Font& operator=(const CR3Font&) = delete;

    // Enable move operations for efficiency
    CR3Font(CR3Font&&) noexcept;
    CR3Font& operator=(CR3Font&&) noexcept;

    /**
     * @brief Invalidates Direct3D device objects
     * @return HRESULT indicating success or failure
     *
     * Original address: 0x140528820
     * Function: ?InvalidateDeviceObjects@CR3Font@@QEAAJXZ
     *
     * Process:
     * 1. Releases device objects if flags are set (offset +100, +104)
     * 2. Calls virtual function at offset +448 for cleanup
     * 3. Resets device flags to 0
     * 4. Calls PrivateRelease for resource cleanup
     */
    std::int64_t InvalidateDeviceObjects() noexcept;

    /**
     * @brief Calculates character index that fits within specified width (ANSI)
     * @param pszText ANSI text string to measure
     * @param nMaxWidth Maximum width in pixels
     * @param nTextLength Length of text to consider
     * @return Number of characters that fit within the width
     *
     * Original address: 0x140527DD0
     * Function: ?CalcStrIndexPitInWidthA@CR3Font@@QEAAHPEBDHH@Z
     *
     * Uses GetTextExtentExPointA with HDC at offset +120 for measurement.
     */
    std::int64_t CalcStrIndexPitInWidthA(const char* pszText, int nMaxWidth, int nTextLength) const noexcept;

    /**
     * @brief Calculates character index that fits within specified width (Unicode)
     * @param pwszText Unicode text string to measure
     * @param nMaxWidth Maximum width in pixels
     * @param nTextLength Length of text to consider
     * @return Number of characters that fit within the width
     *
     * Original address: 0x140527D80
     * Function: ?CalcStrIndexPitInWidthW@CR3Font@@QEAAHPEB_WHH@Z
     *
     * Uses GetTextExtentExPointW with HDC at offset +120 for measurement.
     */
    std::int64_t CalcStrIndexPitInWidthW(const wchar_t* pwszText, int nMaxWidth, int nTextLength) const noexcept;

    /**
     * @brief Initializes Direct3D device objects
     * @param pDevice Direct3D device pointer
     * @param nWidth Texture width
     * @param nHeight Texture height
     * @param dwFlags Font creation flags
     * @return HRESULT indicating success or failure
     *
     * Original address: 0x140526D30
     * Function: ?InitDeviceObjects@CR3Font@@QEAAJPEAUIDirect3DDevice8@@KKK@Z
     *
     * Process:
     * 1. Stores device pointer at offset +0
     * 2. Sets scale factor to 1.0f at offset +80
     * 3. Stores dimensions at offset +144, +148
     * 4. Calls device virtual function at offset +160
     * 5. Stores flags at offset +84
     */
    std::int64_t InitDeviceObjects(IDirect3DDevice8* pDevice, std::uint32_t nWidth, std::uint32_t nHeight, std::uint32_t dwFlags);

    /**
     * @brief Gets current font metrics
     * @return Reference to font metrics
     */
    const FontMetrics& GetFontMetrics() const noexcept { return m_fontMetrics; }

    /**
     * @brief Gets device objects information
     * @return Reference to device objects
     */
    const DeviceObjects& GetDeviceObjects() const noexcept { return m_deviceObjects; }

    /**
     * @brief Checks if font is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const noexcept { return m_bInitialized.load(); }

    /**
     * @brief Gets font scale factor
     * @return Current scale factor
     */
    float GetScaleFactor() const noexcept { return m_fScaleFactor; }

    /**
     * @brief Sets font scale factor
     * @param fScale New scale factor
     */
    void SetScaleFactor(float fScale) noexcept { m_fScaleFactor = fScale; }

    /**
     * @brief Gets font size
     * @return Current font size
     */
    std::uint32_t GetFontSize() const noexcept { return m_dwFontSize; }

    /**
     * @brief Gets font style flags
     * @return Current font style flags
     */
    FontStyle GetFontStyle() const noexcept { return m_fontStyle; }

private:
    // Core device objects (based on decompiled memory layout)
    DeviceObjects m_deviceObjects;              ///< Direct3D and GDI objects
    std::atomic<bool> m_bInitialized{false};    ///< Initialization flag

    // Font properties
    float m_fScaleFactor{1.0f};                 ///< Scale factor (offset +80)
    std::uint32_t m_dwFontSize{12};             ///< Font size (offset +84)
    FontStyle m_fontStyle{FontStyle::NORMAL};   ///< Font style flags (offset +2092)
    std::uint32_t m_dwCharacterSet{0};          ///< Character set (offset +108)

    // Font metrics and dimensions
    FontMetrics m_fontMetrics;                  ///< Font measurement data

    // Device flags and state
    std::atomic<std::uint32_t> m_dwDeviceFlags1{0}; ///< Device flags (offset +100)
    std::atomic<std::uint32_t> m_dwDeviceFlags2{0}; ///< Device flags (offset +104)
    std::uint32_t m_dwBitmapFlags{0};           ///< Bitmap flags (offset +96)

    // Font name and properties
    std::string m_strFontName;                  ///< Font name (offset +16)

    // Thread safety
    mutable std::mutex m_mutex;                 ///< Mutex for thread-safe operations

    // Security and validation
    std::uint32_t m_dwSecurityHash{0};          ///< Security hash for validation
    std::uint64_t m_ullCreationTime{0};         ///< Creation timestamp

    /**
     * @brief Private initialization of font resources
     *
     * Original address: 0x140527E80
     * Function: ?PrivateInit@CR3Font@@AEAAXXZ
     *
     * Complex initialization process:
     * 1. Clears device objects and flags
     * 2. Creates compatible DC and DIB section
     * 3. Calculates font size based on device caps
     * 4. Creates font with specified parameters
     * 5. Sets up text rendering properties
     * 6. Measures character dimensions
     * 7. Allocates memory and clears cache
     */
    void PrivateInit();

    /**
     * @brief Initializes internal data structures
     */
    void InitializeMembers() noexcept;

    /**
     * @brief Cleans up internal resources
     */
    void CleanupMembers() noexcept;

    /**
     * @brief Validates font state for security
     * @return true if state is valid, false otherwise
     */
    bool ValidateState() const noexcept;

    /**
     * @brief Calculates security hash for validation
     * @return Security hash value
     */
    std::uint32_t CalculateSecurityHash() const noexcept;

    /**
     * @brief Allocates memory for font cache
     */
    void MemAllocate();

    /**
     * @brief Clears font cache
     */
    void ClearCache();

    /**
     * @brief Releases private resources
     */
    void PrivateRelease();
};
