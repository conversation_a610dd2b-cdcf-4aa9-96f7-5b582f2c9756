﻿#pragma once

// Generated from decompiled code for NexusPro
// Class: CMyTimer - Custom timer system
// Category: system
// Original address: 0x140438980 (constructor)

#include "../common/WindowsTypes.h"
#include "../common/RFOnlineTypes.h"

// Forward declarations
class CMyTimer;

/**
 * CMyTimer - Authentic RF Online Timer Management Class
 *
 * This class uses the exact structure from the original RF Online server
 * as defined in ZoneServerUD_x64.h (lines 7664-7670)
 *
 * Original decompiled functions:
 * - Constructor: ??0CMyTimer@@QEAA@XZ (0x140438980)
 * - BeginTimer: ?BeginTimer@CMyTimer@@QEAAXK@Z (0x1404389D0)
 * - CountingTimer: ?CountingTimer@CMyTimer@@QEAA_NXZ (0x140438AE0)
 */
class CMyTimer : public ::CMyTimer  // Inherit from authentic RF Online structure
{
public:
    // Constructor/Destructor
    CMyTimer();
    virtual ~CMyTimer();

    // Timer operations (matching decompiled source exactly)
    void BeginTimerAddLapse(unsigned int dwTerm, unsigned int dwAddLapse);
    void BeginTimer(unsigned int dwTerm);
    void CountingAddTickOld(unsigned int dwAddGap);
    char CountingTimer();
    unsigned int GetTerm() const;

    // Authentic RF Online timer methods
    void SetTickTerm(int nTickTerm) { m_nTickTerm = nTickTerm; }
    int GetTickTerm() const { return m_nTickTerm; }
    void SetTickOld(unsigned int dwTickOld) { m_dwTickOld = dwTickOld; }
    unsigned int GetTickOld() const { return m_dwTickOld; }
    void SetOper(bool bOper) { m_bOper = bOper; }
    bool GetOper() const { return m_bOper; }

    // Additional methods for compatibility
    bool IsActive() const { return m_bOper; }
    void Reset();
    void Start(unsigned int dwInterval);
    void Stop();

    // Note: Member variables are inherited from ::CMyTimer structure:
    // CMyTimerVtbl *vfptr;
    // int m_nTickTerm;
    // unsigned int m_dwTickOld;
    // bool m_bOper;
    uint32_t m_dwLastTime;     // Last timer check time
    bool m_bActive;            // Timer active status

private:
    // Private helper methods
    uint32_t GetCurrentTime() const;
};

// C-style function declarations for compatibility with decompiled code
extern "C" {
    void __fastcall CMyTimer_Constructor(CMyTimer *pThis);
    void __fastcall CMyTimer_Destructor(CMyTimer *pThis);
}
