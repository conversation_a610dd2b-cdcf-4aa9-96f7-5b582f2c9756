﻿/**
 * @file CRecallRequest.h
 * @brief Player recall and teleportation request management system
 * @details Comprehensive recall system for player teleportation with security validation
 *
 * Original Memory Addresses:
 * - CRecallRequest::Recall: 0x14024DAC0
 * - CRecallRequest::Regist: 0x14024D530
 * - CRecallRequest::GetOwner: 0x14000A7EF
 * - CRecallRequest::IsBattleModeUse: 0x14000D02B
 * - CRecallRequest::CRecallRequest: 0x30 bytes allocation
 *
 * Decompiled from: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @version 1.0.0
 */

#pragma once

#include "../common/RFProtocol.h"
#include "../common/Types.h"
#include "../common/SecurityManager.h"
#include "../common/WindowsTypes.h"
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>

// Forward declarations
class CCharacter;
class CGameObject;
class CGuildRoomSystem;
class CHolyStoneSystem;
class CMapData;
class CNormalGuildBattle;
class CNormalGuildBattleField;
class CNormalGuildBattleManager;
class CPartyPlayer;
class CPlayer;
class CPlayerDB;
class CPotionMgr;
class CRecallEffectController;

/**
 * @class CRecallRequest
 * @brief Manages player recall and teleportation requests with comprehensive security validation
 *
 * This class handles all aspects of player recall/teleportation including:
 * - Battle mode validation
 * - Guild battle restrictions
 * - Party recall coordination
 * - Stone-based teleportation
 * - Position validation and security checks
 * - Memory management with RAII compliance
 *
 * Original decompiled size: 0x30 bytes (48 bytes)
 * Memory pattern: 0xCCCCCCCC debug initialization preserved
 */
class CRecallRequest {
public:
    /**
     * @brief Default constructor with secure initialization
     *
     * Initializes recall request with debug memory patterns and secure defaults.
     * Original address: Constructor allocates 0x30 bytes
     */
    CRecallRequest();

    /**
     * @brief Constructor with index for pool management
     * @param nIndex Index in the recall request pool
     *
     * Used by CRecallEffectController for pool-based allocation.
     */
    explicit CRecallRequest(int nIndex);

    /**
     * @brief Virtual destructor with RAII cleanup
     *
     * Ensures proper cleanup of resources and maintains memory safety.
     */
    virtual ~CRecallRequest();

    // Disable copy operations for security
    CRecallRequest(const CRecallRequest&) = delete;
    CRecallRequest& operator=(const CRecallRequest&) = delete;

    // Enable move operations for efficiency
    CRecallRequest(CRecallRequest&&) noexcept = default;
    CRecallRequest& operator=(CRecallRequest&&) noexcept = default;

    /**
     * @brief Checks if battle mode recall is enabled
     * @return true if battle mode recall is allowed, false otherwise
     *
     * Original address: 0x14000D02B
     * Function: ?IsBattleModeUse@CRecallRequest@@QEAA_NXZ
     */
    bool IsBattleModeUse() const noexcept;

    /**
     * @brief Gets the owner player of this recall request
     * @return Pointer to the owner CPlayer, nullptr if none
     *
     * Original address: 0x14000A7EF
     * Function: ?GetOwner@CRecallRequest@@QEAAPEAVCPlayer@@XZ
     */
    CPlayer* GetOwner() const noexcept;

    /**
     * @brief Executes the recall operation to destination player
     * @param pkDest Destination player to recall to
     * @param bStone Whether this is a stone-based recall
     * @return Status code: 0=success, 10=guild battle restriction, other=error
     *
     * Original address: 0x14024DAC0
     * Function: ?Recall@CRecallRequest@@QEAAEPEAVCPlayer@@_N@Z
     *
     * Security validations:
     * - Guild battle participation check
     * - Position validation
     * - Map transition security
     * - Anti-cheat verification
     */
    char Recall(CPlayer* pkDest, bool bStone);

    /**
     * @brief Registers a new recall request with comprehensive validation
     * @param pkObj Source player initiating recall
     * @param pkDest Destination character
     * @param bRecallParty Whether to recall entire party
     * @param bStone Whether this is stone-based recall
     * @param bBattleModeUse Whether battle mode recall is enabled
     * @return Status code indicating success or failure reason
     *
     * Original address: 0x14024D530
     * Function: ?Regist@CRecallRequest@@QEAAEPEAVCPlayer@@PEAVCCharacter@@_N22@Z
     *
     * Validation includes:
     * - Guild battle restrictions
     * - Map access permissions
     * - Party coordination
     * - Security checks
     */
    char Regist(CPlayer* pkObj, CCharacter* pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse);

    /**
     * @brief Gets the request index in the pool
     * @return Index value for pool management
     */
    int GetIndex() const noexcept { return m_nIndex; }

    /**
     * @brief Checks if the request is currently active
     * @return true if active, false otherwise
     */
    bool IsActive() const noexcept { return m_bActive.load(); }

    /**
     * @brief Sets the active state of the request
     * @param bActive New active state
     */
    void SetActive(bool bActive) noexcept { m_bActive.store(bActive); }

private:
    // Core member variables (based on decompiled analysis)
    std::atomic<bool> m_bActive{false};           ///< Whether this request is currently active
    std::atomic<bool> m_bBattleModeUse{false};    ///< Battle mode recall enabled flag
    int m_nIndex{-1};                             ///< Index in the request pool

    // Player references with smart pointer management
    std::weak_ptr<CPlayer> m_wpOwner;             ///< Weak reference to owner player
    std::weak_ptr<CCharacter> m_wpDestination;    ///< Weak reference to destination

    // Request parameters
    bool m_bRecallParty{false};                   ///< Whether to recall entire party
    bool m_bStoneRecall{false};                   ///< Whether this is stone-based recall

    // Timing and security
    std::chrono::steady_clock::time_point m_timeCreated;  ///< Request creation time
    std::chrono::steady_clock::time_point m_timeExpiry;   ///< Request expiration time

    // Thread safety
    mutable std::mutex m_mutex;                   ///< Mutex for thread-safe operations

    // Security validation
    uint32_t m_dwSecurityHash{0};                 ///< Security hash for validation

    /**
     * @brief Validates recall security parameters
     * @param pkSource Source player
     * @param pkDest Destination character
     * @return true if validation passes, false otherwise
     */
    bool ValidateRecallSecurity(CPlayer* pkSource, CCharacter* pkDest) const;

    /**
     * @brief Checks guild battle restrictions
     * @param pkPlayer Player to check
     * @return true if recall is allowed, false if restricted
     */
    bool CheckGuildBattleRestrictions(CPlayer* pkPlayer) const;

    /**
     * @brief Validates map transition permissions
     * @param pkSource Source player
     * @param pkDest Destination character
     * @return true if transition is allowed, false otherwise
     */
    bool ValidateMapTransition(CPlayer* pkSource, CCharacter* pkDest) const;
};
