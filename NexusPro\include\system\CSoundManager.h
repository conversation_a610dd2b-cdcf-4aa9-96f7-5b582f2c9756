#pragma once

/*
 * CSoundManager.h - RESOURCE ENHANCED
 * NexusPro RF Online Zone Server
 * 
 * Audio system management
 * Enhanced with Emergency Fix Methodology for RF Online compatibility
 * 
 * Original functionality based on decompiled RF Online server code
 * Enhanced with modern C++ standards and comprehensive audio management
 */

#include "../common/WindowsTypes.h"
#include "../common/Stubs.h"

// Forward declarations
class CMainThread;
class CResourceManager;
class CFileManager;
class CSystemManager;

// Sound formats
enum SoundFormat {
    SOUND_FORMAT_UNKNOWN = 0,
    SOUND_FORMAT_WAV = 1,
    SOUND_FORMAT_MP3 = 2,
    SOUND_FORMAT_OGG = 3,
    SOUND_FORMAT_WMA = 4,
    SOUND_FORMAT_R3S = 5  // RF Online custom format
};

// Sound types
enum SoundType {
    SOUND_TYPE_MUSIC = 0,
    SOUND_TYPE_SFX = 1,
    SOUND_TYPE_VOICE = 2,
    SOUND_TYPE_AMBIENT = 3,
    SOUND_TYPE_UI = 4,
    SOUND_TYPE_SYSTEM = 5,
    SOUND_TYPE_EFFECT = 6
};

// Sound quality levels
enum SoundQuality {
    SOUND_QUALITY_LOW = 0,
    SOUND_QUALITY_MEDIUM = 1,
    SOUND_QUALITY_HIGH = 2,
    SOUND_QUALITY_ULTRA = 3
};

// Sound loading result
enum SoundResult {
    SOUND_SUCCESS = 0,
    SOUND_ERROR_NOT_FOUND = 1,
    SOUND_ERROR_INVALID_FORMAT = 2,
    SOUND_ERROR_OUT_OF_MEMORY = 3,
    SOUND_ERROR_CORRUPTED = 4,
    SOUND_ERROR_UNSUPPORTED = 5,
    SOUND_ERROR_DEVICE_ERROR = 6,
    SOUND_ERROR_UNKNOWN = 7
};

// Sound state
enum SoundState {
    SOUND_STATE_STOPPED = 0,
    SOUND_STATE_PLAYING = 1,
    SOUND_STATE_PAUSED = 2,
    SOUND_STATE_LOADING = 3
};

// Sound information
struct SoundInfo {
    char szSoundName[256];
    char szFilePath[MAX_PATH];
    SoundFormat format;
    SoundType type;
    SoundQuality quality;
    DWORD dwSampleRate;
    DWORD dwChannels;
    DWORD dwBitsPerSample;
    DWORD dwDuration;
    DWORD dwFileSize;
    DWORD dwMemorySize;
    DWORD dwLastAccess;
    DWORD dwAccessCount;
    DWORD dwLoadTime;
    float fVolume;
    float fPitch;
    bool bLoaded;
    bool bCached;
    bool bLooping;
    bool b3D;
    SoundState state;
    void* pSoundData;
    void* pDirectSoundBuffer;  // DirectSound buffer object
};

// Sound cache entry
struct SoundCacheEntry {
    SoundInfo info;
    DWORD dwCacheTime;
    DWORD dwLastUsed;
    bool bLocked;
    bool bDirty;
};

// Sound manager configuration
struct SoundManagerConfig {
    bool bEnabled;
    bool bCacheEnabled;
    bool bCompressionEnabled;
    bool b3DEnabled;
    bool bAsyncLoadingEnabled;
    bool bQualityScalingEnabled;
    SoundQuality defaultQuality;
    DWORD dwMaxCacheSize;
    DWORD dwMaxCacheEntries;
    DWORD dwCacheTimeout;
    DWORD dwMaxSoundChannels;
    DWORD dwBufferSize;
    float fMasterVolume;
    float fMusicVolume;
    float fSFXVolume;
    float fVoiceVolume;
    char szSoundRootPath[MAX_PATH];
    char szMusicPath[MAX_PATH];
    char szSFXPath[MAX_PATH];
    char szVoicePath[MAX_PATH];
};

// Sound manager statistics
struct SoundManagerStatistics {
    DWORD dwTotalSounds;
    DWORD dwLoadedSounds;
    DWORD dwCachedSounds;
    DWORD dwPlayingSounds;
    DWORD dwSoundLoads;
    DWORD dwSoundUnloads;
    DWORD dwSoundPlays;
    DWORD dwSoundStops;
    DWORD dwCacheHits;
    DWORD dwCacheMisses;
    DWORD dwTotalMemoryUsed;
    DWORD dwCacheMemoryUsed;
    DWORD dwAverageLoadTime;
    DWORD dwMaxLoadTime;
    DWORD dwLastLoadTime;
    DWORD dwActiveChannels;
    DWORD dwMaxChannelsUsed;
};

/*
 * CSoundManager - Audio system management
 * Manages all sound operations, loading, caching, and playback
 * Enhanced with comprehensive audio management and RF Online compatibility
 */
class CSoundManager {
public:
    // Singleton access
    static CSoundManager& Instance();

    // Core lifecycle methods
    bool Initialize();
    void Shutdown();
    void Update();

    // Sound loading methods
    SoundResult LoadSound(const char* szSoundName, const char* szFilePath);
    SoundResult LoadSound(const char* szSoundName, const char* szFilePath, SoundType type);
    SoundResult LoadSound(const char* szSoundName, const char* szFilePath, SoundType type, SoundQuality quality);
    SoundResult LoadMusic(const char* szSoundName);
    SoundResult LoadSFX(const char* szSoundName);
    SoundResult LoadVoice(const char* szSoundName);

    // Sound playback methods
    SoundResult PlaySound(const char* szSoundName);
    SoundResult PlaySound(const char* szSoundName, float fVolume);
    SoundResult PlaySound(const char* szSoundName, float fVolume, float fPitch);
    SoundResult PlaySound(const char* szSoundName, float fVolume, float fPitch, bool bLooping);
    SoundResult StopSound(const char* szSoundName);
    SoundResult PauseSound(const char* szSoundName);
    SoundResult ResumeSound(const char* szSoundName);

    // Sound control methods
    void StopAllSounds();
    void PauseAllSounds();
    void ResumeAllSounds();
    void SetMasterVolume(float fVolume);
    void SetMusicVolume(float fVolume);
    void SetSFXVolume(float fVolume);
    void SetVoiceVolume(float fVolume);

    // Sound access methods
    bool GetSoundInfo(const char* szSoundName, SoundInfo& info);
    bool IsSoundLoaded(const char* szSoundName);
    bool IsSoundPlaying(const char* szSoundName);
    SoundState GetSoundState(const char* szSoundName);

    // Sound management methods
    void UnloadSound(const char* szSoundName);
    void UnloadAllSounds();
    void UnloadSoundsByType(SoundType type);
    void ReloadSound(const char* szSoundName);
    void ReloadAllSounds();

    // Sound caching methods
    bool CacheSound(const char* szSoundName);
    void UncacheSound(const char* szSoundName);
    bool IsSoundCached(const char* szSoundName);
    void FlushCache();
    void OptimizeCache();

    // Sound utility methods
    SoundFormat DetectSoundFormat(const char* szFilePath);
    DWORD CalculateSoundMemorySize(DWORD dwSampleRate, DWORD dwChannels, DWORD dwBitsPerSample, DWORD dwDuration);
    bool ValidateSoundFile(const char* szFilePath);
    bool CreateSoundFromMemory(const char* szSoundName, const void* pData, DWORD dwSize);

    // Configuration and statistics
    const SoundManagerConfig& GetConfig() const { return m_Config; }
    const SoundManagerStatistics& GetStatistics() const { return m_Statistics; }
    void ResetStatistics();

    // Utility methods
    bool IsSoundManagerEnabled() const { return m_bInitialized && m_Config.bEnabled; }
    void LogSoundOperation(const char* szOperation, const char* szSoundName, SoundResult result);

private:
    // Private constructor for singleton
    CSoundManager();
    ~CSoundManager();

    // Prevent copying
    CSoundManager(const CSoundManager&) = delete;
    CSoundManager& operator=(const CSoundManager&) = delete;

    // Internal methods
    bool LoadConfiguration();
    bool InitializeDirectSound();
    bool InitializeCache();
    void ShutdownDirectSound();
    void ShutdownCache();
    SoundResult LoadSoundFromFile(const char* szSoundName, const char* szFilePath, SoundType type, SoundQuality quality);
    SoundResult ProcessSoundData(SoundInfo& info, const void* pData, DWORD dwSize);
    void UpdateStatistics();
    void UpdateCache();
    void UpdatePlayingSounds();

    // Cache management
    SoundCacheEntry* FindCacheEntry(const char* szSoundName);
    SoundCacheEntry* CreateCacheEntry(const SoundInfo& info);
    void RemoveCacheEntry(const char* szSoundName);
    void CleanupExpiredCacheEntries();

    // Format detection and processing
    SoundFormat DetectFormatFromHeader(const void* pData, DWORD dwSize);
    bool ProcessWAVSound(SoundInfo& info, const void* pData, DWORD dwSize);
    bool ProcessMP3Sound(SoundInfo& info, const void* pData, DWORD dwSize);
    bool ProcessOGGSound(SoundInfo& info, const void* pData, DWORD dwSize);
    bool ProcessR3SSound(SoundInfo& info, const void* pData, DWORD dwSize);

    // DirectSound management
    bool CreateSoundBuffer(SoundInfo& info);
    void ReleaseSoundBuffer(SoundInfo& info);
    bool FillSoundBuffer(SoundInfo& info);

    // Member variables
    static CSoundManager* s_pInstance;
    static std::mutex s_InstanceMutex;

    bool m_bInitialized;
    bool m_bDirectSoundInitialized;
    bool m_bCacheActive;
    SoundManagerConfig m_Config;
    SoundManagerStatistics m_Statistics;

    // Sound storage
    std::map<std::string, SoundInfo> m_Sounds;
    std::map<std::string, SoundCacheEntry> m_SoundCache;
    DWORD m_dwCurrentCacheSize;
    DWORD m_dwCacheEntryCount;

    // DirectSound objects
    void* m_pDirectSound;        // IDirectSound8 interface
    void* m_pPrimaryBuffer;      // Primary sound buffer

    // Synchronization
    mutable std::mutex m_ConfigMutex;
    mutable std::mutex m_StatisticsMutex;
    mutable std::mutex m_SoundMutex;
    mutable std::mutex m_CacheMutex;

    // Performance tracking
    DWORD m_dwLastUpdateTime;
    DWORD m_dwUpdateInterval;
    DWORD m_dwLoadStartTime;
};
