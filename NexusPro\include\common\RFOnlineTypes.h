#pragma once

/*
 * RFOnlineTypes.h - AUTHENTIC RF ONLINE TYPES
 * NexusPro RF Online Zone Server
 * 
 * Authentic RF Online type definitions extracted from ZoneServerUD_x64.h
 * These are the exact structures used in the original RF Online server
 * 
 * Source: D:\RF-Online_NexusProtection\NexusProtection\decompiled\ZoneServerUD_x64.h
 */

#include "WindowsTypes.h"

// RF Online basic type definitions (from ZoneServerUD_x64.h lines 7-10)
#define __int8 char
#define __int16 short
#define __int32 int
#define __int64 long long

// Forward declarations for RF Online structures
struct CMyTimerVtbl;
struct strFILEVtbl;
struct CNetCriticalSection;

/*
 * CMyTimer - Original RF Online Timer Structure
 * Address: Line 7664-7670 in ZoneServerUD_x64.h
 * Original memory layout preserved
 */
struct __declspec(align(8)) CMyTimer
{
    CMyTimerVtbl *vfptr;
    int m_nTickTerm;
    unsigned int m_dwTickOld;
    bool m_bOper;
};

/*
 * CMyTimerVtbl - Original RF Online Timer Virtual Function Table
 * Address: Line 7673-7676 in ZoneServerUD_x64.h
 */
struct CMyTimerVtbl
{
    void *(__cdecl *__vecDelDtor)(CMyTimer *this, unsigned int);
};

/*
 * CLogFile - Original RF Online Log File Structure
 * Address: Line 6198-6203 in ZoneServerUD_x64.h
 * Original memory layout preserved
 */
struct __declspec(align(8)) CLogFile
{
    char m_szFileName[128];
    unsigned int m_dwLogCount;
    int m_bWriteAble;
    CNetCriticalSection m_cs;
};

/*
 * strFILE - Original RF Online File Structure
 * Address: Line 41730-41736 in ZoneServerUD_x64.h
 * Original memory layout preserved
 */
struct __declspec(align(8)) strFILE
{
    strFILEVtbl *vfptr;
    char *m_pLoadStr;
    char *m_pReadStr;
    unsigned int m_dwLoadSize;
};

/*
 * strFILEVtbl - Original RF Online File Virtual Function Table
 * Address: Line 41739-41742 in ZoneServerUD_x64.h
 */
struct strFILEVtbl
{
    void *(__cdecl *__vecDelDtor)(strFILE *this, unsigned int);
};

/*
 * _MERGE_FILE - Original RF Online Merge File Structure
 * Address: Line 10055-10062 in ZoneServerUD_x64.h
 */
struct _MERGE_FILE
{
    union {
        struct {
            unsigned int signature;
            unsigned int version;
        };
        unsigned __int64 header;
    } ___u0;
    unsigned int file_length;
    unsigned __int16 name_cnt;
    unsigned __int16 cnt;
    unsigned int start_index;
};

/*
 * CMergeFile - Original RF Online Merge File Manager Structure
 * Address: Line 10065-10073 in ZoneServerUD_x64.h
 */
struct CMergeFile
{
    char mFileName[256];
    _MERGE_FILE *mMergeFNF;
    unsigned int *mOffset;
    unsigned int *mIndex;
    unsigned int mCnt;
    unsigned int mHeaderSize;
};

/*
 * CMergeFileManager - Original RF Online Merge File Manager
 * Address: Line 10076-10081 in ZoneServerUD_x64.h
 */
struct CMergeFileManager
{
    char mPath[256];
    unsigned int mPathNameLeng;
    unsigned int mMergeFileNum;
    CMergeFile *mMergeFile;
};

/*
 * _FILETIME - Original RF Online File Time Structure
 * Address: Line 8041-8045 in ZoneServerUD_x64.h
 */
struct _FILETIME
{
    unsigned int dwLowDateTime;
    unsigned int dwHighDateTime;
};

/*
 * CTimer - Original RF Online Timer Structure
 * Address: Line 9526-9531 in ZoneServerUD_x64.h
 */
struct CTimer
{
    float mLoopTime;
    float mTime;
    float mRealTime;
    float mMinFPS;
};

/*
 * SF_Timer - Original RF Online SF Timer Structure
 * Address: Line 12423-12427 in ZoneServerUD_x64.h
 */
struct SF_Timer
{
    unsigned int m_dwLastCheckTime;
    unsigned int m_dwGapCheckTime;
};

/*
 * CAITimer - Original RF Online AI Timer Structure
 * Address: Line 13124-13129 in ZoneServerUD_x64.h
 */
struct CAITimer
{
    unsigned int m_BefTime;
    unsigned int m_Delay;
    unsigned int m_DDelay;
};

/*
 * CNetTimer - Original RF Online Network Timer Structure
 * Address: Line 13457-13462 in ZoneServerUD_x64.h
 */
struct __declspec(align(4)) CNetTimer
{
    int m_nTickTerm;
    unsigned int m_dwTickOld;
    bool m_bOper;
};

/*
 * C24Timer - Original RF Online 24-Hour Timer Structure
 * Address: Line 41723-41727 in ZoneServerUD_x64.h
 */
struct C24Timer
{
    unsigned int m_dwBaseTickTime;
    unsigned int m_dwBase24Time;
};

/*
 * MyTimer::TIME - Original RF Online Time Structure
 * Address: Line 18854-18859 in ZoneServerUD_x64.h
 */
struct MyTimer_TIME
{
    int _nYear;
    int _nMonth;
    int _nDay;
    int _nHour;
    int _nMinute;
    int _nSecond;
};

/*
 * _SOUND_ENTITY_LIST - Original RF Online Sound Entity Structure
 * Address: Line 10121-10125 in ZoneServerUD_x64.h
 */
struct _SOUND_ENTITY_LIST
{
    unsigned __int16 ID;
    char Name[62];
};

/*
 * _SOUND_ENTITIES_LIST - Original RF Online Sound Entities Structure
 * Address: Line 10128-10133 in ZoneServerUD_x64.h
 */
struct _SOUND_ENTITIES_LIST
{
    unsigned __int16 ID;
    unsigned __int16 EventTime;
    unsigned int Flag;
    float Scale;
};

/*
 * _READ_SOUND_ENTITY_LIST - Original RF Online Read Sound Entity Structure
 * Address: Line 23181-23184 in ZoneServerUD_x64.h
 */
struct _READ_SOUND_ENTITY_LIST
{
    char name[64];
};

/*
 * _READ_SOUND_ENTITIES_LIST - Original RF Online Read Sound Entities Structure
 * Address: Line 23187-23192 in ZoneServerUD_x64.h
 */
struct _READ_SOUND_ENTITIES_LIST
{
    unsigned __int16 id;
    unsigned __int16 event_time;
    unsigned int flag;
    float scale;
};

/*
 * _MEMORYSTATUS - Original RF Online Memory Status Structure
 * Address: Line 34193-34203 in ZoneServerUD_x64.h
 */
struct _MEMORYSTATUS
{
    unsigned int dwLength;
    unsigned int dwMemoryLoad;
    unsigned __int64 dwTotalPhys;
    unsigned __int64 dwAvailPhys;
    unsigned __int64 dwTotalPageFile;
    unsigned __int64 dwAvailPageFile;
    unsigned __int64 dwTotalVirtual;
    unsigned __int64 dwAvailVirtual;
};

/*
 * _MEMORY_BASIC_INFORMATION - Original RF Online Memory Basic Information
 * Address: Line 34206-34211 in ZoneServerUD_x64.h
 */
struct __declspec(align(8)) _MEMORY_BASIC_INFORMATION
{
    void *BaseAddress;
    void *AllocationBase;
    unsigned int AllocationProtect;
    unsigned __int64 RegionSize;
    unsigned int State;
    unsigned int Protect;
    unsigned int Type;
};

/*
 * _MEMORYSTATUSEX - Original RF Online Extended Memory Status
 * Address: Line 50994-50999 in ZoneServerUD_x64.h
 */
struct _MEMORYSTATUSEX
{
    unsigned int dwLength;
    unsigned int dwMemoryLoad;
    unsigned __int64 ullTotalPhys;
    unsigned __int64 ullAvailPhys;
    unsigned __int64 ullTotalPageFile;
    unsigned __int64 ullAvailPageFile;
    unsigned __int64 ullTotalVirtual;
    unsigned __int64 ullAvailVirtual;
    unsigned __int64 ullAvailExtendedVirtual;
};

// RF Online compatibility macros
#define RF_ALIGN_8 __declspec(align(8))
#define RF_ALIGN_4 __declspec(align(4))

// RF Online memory patterns
#define RF_STACK_PATTERN 0xCCCCCCCC
#define RF_MEMORY_SIGNATURE 0x12345678

// RF Online function calling conventions
#define RF_CDECL __cdecl
#define RF_STDCALL __stdcall
#define RF_FASTCALL __fastcall
