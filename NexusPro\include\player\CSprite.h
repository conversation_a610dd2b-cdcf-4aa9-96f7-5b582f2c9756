﻿/**
 * @file CSprite.h
 * @brief Sprite and texture management system for visual representation
 * @details Comprehensive sprite system for Direct3D texture management and page-based rendering
 *
 * Original Memory Addresses:
 * - CSprite::GetD3D8TexPtr: 0x140521BB0
 * - CSprite::InitPageSprite: 0x1405206A0
 * - InitSpriteManager: 0x140501B80
 * - ReleaseSpriteManager: 0x140501DC0
 * - RestoreSpriteManager: 0x140501D20
 *
 * Memory Layout Analysis:
 * - DWORD at offset +4: Page count/limit
 * - WORD at offset +16: Page width
 * - WORD at offset +18: Page height
 * - DWORD at offset +8: Initialization flag
 * - DWORD at offset +52: Current page X
 * - DWORD at offset +56: Current page Y
 * - QWORD at offset +72: Texture data array
 *
 * Decompiled from: D:\RF-Online_NexusProtection\NexusProtection\decompiled\player
 *
 * <AUTHOR> Development Team
 * @date 2025-01-13
 * @version 1.0.0
 */

#pragma once

#include "../common/RFProtocol.h"
#include "../common/Types.h"
#include "../common/SecurityManager.h"
#include "../common/WindowsTypes.h"
#include <memory>
#include <vector>
#include <array>
#include <atomic>
#include <mutex>

// Forward declarations
class CD3DApplication;
class CSurface;
struct IDirect3DTexture8;
struct IDirect3DDevice8;

/**
 * @struct SpritePageInfo
 * @brief Information about a sprite page for texture management
 */
struct SpritePageInfo {
    std::uint16_t wWidth{0};        ///< Page width in pixels
    std::uint16_t wHeight{0};       ///< Page height in pixels
    std::uint32_t dwFlags{0};       ///< Page flags and properties
    void* pTextureData{nullptr};    ///< Pointer to texture data

    SpritePageInfo() noexcept = default;

    SpritePageInfo(std::uint16_t width, std::uint16_t height, std::uint32_t flags = 0) noexcept
        : wWidth(width), wHeight(height), dwFlags(flags) {}

    bool IsValid() const noexcept {
        return wWidth > 0 && wHeight > 0;
    }

    void Clear() noexcept {
        wWidth = 0;
        wHeight = 0;
        dwFlags = 0;
        pTextureData = nullptr;
    }
};

/**
 * @struct TextureEntry
 * @brief Entry in the texture array for sprite management
 */
struct TextureEntry {
    void* pTexture{nullptr};        ///< Direct3D texture pointer
    std::uint32_t dwSize{0};        ///< Texture size in bytes
    std::uint32_t dwFlags{0};       ///< Texture flags
    std::uint64_t ullLastUsed{0};   ///< Last access timestamp

    TextureEntry() noexcept = default;

    bool IsValid() const noexcept {
        return pTexture != nullptr && dwSize > 0;
    }

    void Clear() noexcept {
        pTexture = nullptr;
        dwSize = 0;
        dwFlags = 0;
        ullLastUsed = 0;
    }
};

/**
 * @class CSprite
 * @brief Manages sprite rendering and Direct3D texture operations with comprehensive security
 *
 * This class handles all aspects of sprite management including:
 * - Direct3D texture pointer management
 * - Page-based sprite initialization
 * - Texture array management with bounds checking
 * - Memory-safe texture access with validation
 * - Integration with sprite manager systems
 * - RAII-compliant resource management
 *
 * Memory Layout (based on decompiled analysis):
 * - Offset +4: Page count limit (DWORD)
 * - Offset +8: Initialization flag (DWORD)
 * - Offset +16: Page width (WORD)
 * - Offset +18: Page height (WORD)
 * - Offset +52: Current page X (DWORD)
 * - Offset +56: Current page Y (DWORD)
 * - Offset +72: Texture data array (QWORD)
 */
class CSprite {
public:
    /**
     * @brief Default constructor with secure initialization
     *
     * Initializes sprite with debug memory patterns and secure defaults.
     */
    CSprite();

    /**
     * @brief Virtual destructor with RAII cleanup
     *
     * Ensures proper cleanup of Direct3D resources and maintains memory safety.
     */
    virtual ~CSprite();

    // Disable copy operations for security
    CSprite(const CSprite&) = delete;
    CSprite& operator=(const CSprite&) = delete;

    // Enable move operations for efficiency
    CSprite(CSprite&&) noexcept;
    CSprite& operator=(CSprite&&) noexcept;

    /**
     * @brief Gets Direct3D texture pointer with bounds validation
     * @param nPageIndex Page index (0xFFFF uses current page)
     * @param nTextureIndex Texture index within page (0xFFFF uses current texture)
     * @return Pointer to Direct3D texture, nullptr if invalid
     *
     * Original address: 0x140521BB0
     * Function: ?GetD3D8TexPtr@CSprite@@QEAAPEAXKK@Z
     *
     * Security validations:
     * - Bounds checking for page and texture indices
     * - Null pointer validation
     * - Memory access validation
     * - Anti-buffer overflow protection
     */
    void* GetD3D8TexPtr(std::uint32_t nPageIndex = 0xFFFF, std::uint32_t nTextureIndex = 0xFFFF) const noexcept;

    /**
     * @brief Initializes page sprite with specified dimensions
     * @param nWidth Page width in pixels
     * @param nHeight Page height in pixels
     *
     * Original address: 0x1405206A0
     * Function: ?InitPageSprite@CSprite@@QEAAXGG@Z
     *
     * Sets up sprite page with:
     * - Page dimensions (width/height)
     * - Initialization flag (set to 1)
     * - Memory layout preparation
     */
    void InitPageSprite(std::int16_t nWidth, std::int16_t nHeight) noexcept;

    /**
     * @brief Gets current page information
     * @return Reference to current page info
     */
    const SpritePageInfo& GetCurrentPageInfo() const noexcept { return m_currentPage; }

    /**
     * @brief Gets page count
     * @return Number of pages available
     */
    std::uint32_t GetPageCount() const noexcept { return m_dwPageCount; }

    /**
     * @brief Checks if sprite is initialized
     * @return true if initialized, false otherwise
     */
    bool IsInitialized() const noexcept { return m_bInitialized.load(); }

    /**
     * @brief Gets current page coordinates
     * @param nPageX Reference to receive page X coordinate
     * @param nPageY Reference to receive page Y coordinate
     */
    void GetCurrentPageCoords(std::uint32_t& nPageX, std::uint32_t& nPageY) const noexcept;

    /**
     * @brief Sets current page coordinates
     * @param nPageX New page X coordinate
     * @param nPageY New page Y coordinate
     */
    void SetCurrentPageCoords(std::uint32_t nPageX, std::uint32_t nPageY) noexcept;

    /**
     * @brief Validates texture access parameters
     * @param nPageIndex Page index to validate
     * @param nTextureIndex Texture index to validate
     * @return true if parameters are valid, false otherwise
     */
    bool ValidateTextureAccess(std::uint32_t nPageIndex, std::uint32_t nTextureIndex) const noexcept;

    /**
     * @brief Gets texture entry at specified indices
     * @param nPageIndex Page index
     * @param nTextureIndex Texture index
     * @return Pointer to texture entry, nullptr if invalid
     */
    const TextureEntry* GetTextureEntry(std::uint32_t nPageIndex, std::uint32_t nTextureIndex) const noexcept;

private:
    // Core sprite properties (based on decompiled memory layout)
    std::atomic<bool> m_bInitialized{false};      ///< Initialization flag (offset +8)
    std::uint32_t m_dwPageCount{0};               ///< Page count limit (offset +4)

    // Current page information
    SpritePageInfo m_currentPage;                 ///< Current page info (offset +16/+18)
    std::atomic<std::uint32_t> m_dwCurrentPageX{0}; ///< Current page X (offset +52)
    std::atomic<std::uint32_t> m_dwCurrentPageY{0}; ///< Current page Y (offset +56)

    // Texture management
    std::vector<std::vector<TextureEntry>> m_textureArray; ///< Texture data array (offset +72)
    std::vector<std::uint16_t> m_pageTextureCounts;        ///< Texture count per page

    // Thread safety
    mutable std::mutex m_mutex;                   ///< Mutex for thread-safe operations

    // Security and validation
    std::uint32_t m_dwSecurityHash{0};            ///< Security hash for validation
    std::uint64_t m_ullCreationTime{0};           ///< Creation timestamp

    /**
     * @brief Initializes internal data structures
     */
    void InitializeMembers() noexcept;

    /**
     * @brief Cleans up internal resources
     */
    void CleanupMembers() noexcept;

    /**
     * @brief Validates sprite state for security
     * @return true if state is valid, false otherwise
     */
    bool ValidateState() const noexcept;

    /**
     * @brief Calculates security hash for validation
     * @return Security hash value
     */
    std::uint32_t CalculateSecurityHash() const noexcept;

    /**
     * @brief Gets texture pointer with internal validation
     * @param nPageIndex Page index
     * @param nTextureIndex Texture index
     * @return Raw texture pointer
     */
    void* GetTexturePointerInternal(std::uint32_t nPageIndex, std::uint32_t nTextureIndex) const noexcept;
};
