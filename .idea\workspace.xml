<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="12570add-606c-42ee-9c48-3c3bd243aa6e" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zcp22q6f3uKQy6q4oPfbmxycNO" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/RF-Online_NexusProtection/NexusProtection&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="12570add-606c-42ee-9c48-3c3bd243aa6e" name="Changes" comment="" />
      <created>1752040004558</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752040004558</updated>
      <workItem from="1752040006136" duration="22135000" />
      <workItem from="1752065029606" duration="11398000" />
      <workItem from="1752102106142" duration="619000" />
      <workItem from="1752102778770" duration="24806000" />
      <workItem from="1752136622242" duration="41000" />
      <workItem from="1752136679387" duration="15164000" />
      <workItem from="1752155559414" duration="8301000" />
      <workItem from="1752187716688" duration="9126000" />
      <workItem from="1752199200156" duration="15387000" />
      <workItem from="1752217568959" duration="51000" />
      <workItem from="1752381963804" duration="7220000" />
      <workItem from="1752390871554" duration="5916000" />
      <workItem from="1752403569994" duration="5329000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>